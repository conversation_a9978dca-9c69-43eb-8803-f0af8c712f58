# 生产任务关联查询接口集成示例

## 接口说明

### 1. 根据资产ID查询关联的生产任务
- **接口地址**: `GET /production/task/by-asset/{assetId}`
- **权限要求**: `production:task:query`
- **参数**: 
  - `assetId`: 资产ID
- **返回**: 关联的生产任务列表

### 2. 根据库存物品ID查询关联的生产任务
- **接口地址**: `GET /production/task/by-inventory/{itemId}`
- **权限要求**: `production:task:query`
- **参数**: 
  - `itemId`: 物品ID
- **返回**: 关联的生产任务列表

## 前端集成示例

### 1. 在资产详情页面中集成

```vue
<template>
  <div class="asset-detail">
    <!-- 资产基本信息 -->
    <el-card class="box-card">
      <div slot="header">
        <span>资产详情</span>
      </div>
      <!-- 资产详情内容 -->
    </el-card>

    <!-- 关联的生产任务 -->
    <related-tasks-list
      relation-type="asset"
      :relation-id="assetId"
      title="关联的生产任务"
    />
  </div>
</template>

<script>
import RelatedTasksList from "@/components/production/RelatedTasksList"

export default {
  name: "AssetDetail",
  components: {
    RelatedTasksList
  },
  data() {
    return {
      assetId: null
    }
  },
  created() {
    this.assetId = this.$route.params.assetId
  }
}
</script>
```

### 2. 在库存详情页面中集成

```vue
<template>
  <div class="inventory-detail">
    <!-- 库存基本信息 -->
    <el-card class="box-card">
      <div slot="header">
        <span>库存详情</span>
      </div>
      <!-- 库存详情内容 -->
    </el-card>

    <!-- 关联的生产任务 -->
    <related-tasks-list
      relation-type="inventory"
      :relation-id="itemId"
      title="关联的生产任务"
    />
  </div>
</template>

<script>
import RelatedTasksList from "@/components/production/RelatedTasksList"

export default {
  name: "InventoryDetail",
  components: {
    RelatedTasksList
  },
  data() {
    return {
      itemId: null
    }
  },
  created() {
    this.itemId = this.$route.params.itemId
  }
}
</script>
```

### 3. 直接调用API接口

```javascript
import { getTasksByAssetId, getTasksByInventoryId } from "@/api/production/task"

// 查询资产关联的任务
getTasksByAssetId('asset123').then(response => {
  console.log('资产关联的任务:', response.data)
})

// 查询库存关联的任务
getTasksByInventoryId('item456').then(response => {
  console.log('库存关联的任务:', response.data)
})
```

## 返回数据格式

```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "taskId": "task001",
      "taskName": "生产任务A",
      "taskCode": "PROD-2025-001",
      "status": 3,
      "statusName": "执行中",
      "responsibleUserId": 123,
      "responsibleUserName": "张三",
      "createTime": "2025-01-20 10:00:00",
      "priorityLevel": 1,
      "priorityLevelName": "高"
    }
  ]
}
```

## 组件属性说明

### RelatedTasksList 组件

| 属性 | 类型 | 必填 | 说明 |
|------|------|------|------|
| relationType | String | 是 | 关联类型，'asset' 或 'inventory' |
| relationId | String | 是 | 关联ID（资产ID或物品ID） |
| title | String | 否 | 标题，默认为"关联的生产任务" |

### 组件方法

| 方法名 | 说明 |
|--------|------|
| loadTasks() | 加载关联的任务列表 |
| refreshTasks() | 刷新任务列表 |
| handleView(row) | 查看任务详情 |

## 使用场景

1. **资产管理模块**: 在资产详情页面显示该资产参与的所有生产任务
2. **库存管理模块**: 在物品详情页面显示该物品被使用的所有生产任务
3. **生产计划**: 了解资产和物品的使用情况，优化生产计划
4. **追溯管理**: 追踪资产和物品在生产过程中的使用历史

## 注意事项

1. 确保用户有 `production:task:query` 权限
2. 组件会自动处理加载状态和错误提示
3. 点击"查看详情"会跳转到任务详情页面
4. 支持实时刷新数据
